import { generateId } from './internal/id.js';
import { getStore } from './internal/storage.js';
import { safeSend } from './internal/transport.js';
import { getClientContext } from './internal/ua.js';

/**
 * Minimal, dependency-free public API
 */
export default class VisitorTracker {
  /**
   * @param {object} options
   * @param {string} [options.endpoint] - Server endpoint to receive events (POST). If not provided, events stay client-side only.
   * @param {('auto'|'localStorage'|'cookie'|'memory')} [options.storage='auto'] - Where to persist the visitor id.
   * @param {object} [options.meta] - Static metadata merged into every event (e.g. { projectId: 'abc' }).
   */
  constructor(options = {}) {
    this.endpoint = options.endpoint || null;
    this.storage = getStore(options.storage || 'auto');
    this.meta = { ...(options.meta || {}) };

    this._id = this.storage.get('uvt_id');
    if (!this._id) {
      this._id = generateId();
      this.storage.set('uvt_id', this._id);
    }
  }

  /** Get or create the unique visitor id. */
  getId() {
    return this._id;
  }

  /** Track a page view. */
  trackPage(path = (typeof location !== 'undefined' ? location.pathname : '/'), data = {}) {
    return this._emit('page_view', { path, ...data });
  }

  /** Track a custom event. */
  trackEvent(name, data = {}) {
    if (!name) throw new Error('trackEvent(name, data) requires a non-empty name');
    return this._emit(name, data);
  }

  /** Clear the stored visitor id (for debugging / opt-out flows). */
  resetId() {
    this.storage.remove('uvt_id');
    this._id = generateId();
    this.storage.set('uvt_id', this._id);
    return this._id;
  }

  /**
   * Internal emit helper
   * @private
   */
  _emit(type, data) {
    const ts = new Date().toISOString();
    const payload = {
      type,
      ts,
      visitorId: this._id,
      client: getClientContext(),
      ...this.meta,
      data
    };

    if (!this.endpoint) {
      // no-op send; return payload for devs who want to intercept
      return Promise.resolve({ ok: true, delivered: false, payload });
    }

    return safeSend(this.endpoint, payload);
  }
}

// Named helpers for convenience
export function getVisitorId(options = {}) {
  const storage = getStore(options.storage || 'auto');
  let id = storage.get('uvt_id');
  if (!id) {
    id = generateId();
    storage.set('uvt_id', id);
  }
  return id;
}

export function trackVisit(endpoint, path = (typeof location !== 'undefined' ? location.pathname : '/'), meta = {}, data = {}) {
  const tracker = new VisitorTracker({ endpoint, meta });
  return tracker.trackPage(path, data);
}

export { getMemoryStore, getCookieStore, getLocalStore } from './internal/storage.js';