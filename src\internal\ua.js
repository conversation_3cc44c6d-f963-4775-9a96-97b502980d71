/** Collect lightweight, privacy-first client context (no PII). */
export function getClientContext() {
  if (typeof navigator === 'undefined') return { env: 'node' };
  const tz = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const lang = navigator.language || (navigator.languages && navigator.languages[0]);
  const ua = navigator.userAgent || 'unknown';
  const screenInfo = (typeof screen !== 'undefined')
    ? { w: screen.width, h: screen.height, dpr: (window.devicePixelRatio || 1) }
    : undefined;
  return { env: 'browser', tz, lang, ua, screen: screenInfo }; // no IP, no precise location
}