/** Send with navigator.sendBeacon if possible, fallback to fetch POST. */
export async function safeSend(endpoint, payload) {
  const body = JSON.stringify(payload);
  // Try Beacon in browsers
  try {
    if (typeof navigator !== 'undefined' && navigator.sendBeacon) {
      const ok = navigator.sendBeacon(endpoint, new Blob([body], { type: 'application/json' }));
      return Promise.resolve({ ok, delivered: ok, payload });
    }
  } catch {}

  // Fallback to fetch
  try {
    const res = await fetch(endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      keepalive: true,
      body
    });
    return { ok: res.ok, status: res.status, delivered: res.ok, payload };
  } catch (err) {
    return { ok: false, delivered: false, error: String(err), payload };
  }
}