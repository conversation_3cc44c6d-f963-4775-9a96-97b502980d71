export type StorageMode = 'auto' | 'localStorage' | 'cookie' | 'memory';

export interface TrackerOptions {
    endpoint?: string | null;
    storage?: StorageMode;
    meta?: Record<string, any>;
}

export interface SendResult {
    ok: boolean;
    delivered: boolean;
    status?: number;
    error?: string;
    payload: any;
}

export default class VisitorTracker {
    constructor(options?: TrackerOptions);
    getId(): string;
    trackPage(path?: string, data?: Record<string, any>): Promise<SendResult>;
    trackEvent(name: string, data?: Record<string, any>): Promise<SendResult>;
    resetId(): string;
}

export function getVisitorId(options?: { storage?: StorageMode }): string;
export function trackVisit(endpoint: string, path?: string, meta?: Record<string, any>, data?: Record<string, any>): Promise<SendResult>;

export function getMemoryStore(): any;
export function getLocalStore(): any;
export function getCookieStore(): any;