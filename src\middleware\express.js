/**
 * Tiny Express-compatible handler to receive events from the browser
 * and store them. You can plug in any storage implementation.
 *
 * Usage (Node):
 *   import express from 'express';
 *   import { createReceiver } from 'unique-visitor-tracker/middleware/express';
 *
 *   const app = express();
 *   app.use(express.json());
 *   const events = []; // naive in-memory store
 *   app.post('/api/track', createReceiver({ save: (evt) => events.push(evt) }));
 *   app.get('/api/track', (req, res) => res.json(events));
 */

/** @typedef {(event: object, req: import('http').IncomingMessage) => Promise<void>|void} SaveFn */

/**
 * @param {object} options
 * @param {SaveFn} options.save - required: async function that persists an event
 * @param {(req: import('http').IncomingMessage) => boolean} [options.allow] - optional gate to accept/deny requests
 */
export function createReceiver({ save, allow } = {}) {
  if (typeof save !== 'function') throw new Error('createR<PERSON>eiver requires a { save } function');
  return async function receiver(req, res) {
    try {
      if (allow && !allow(req)) {
        res.status(403).json({ ok: false, error: 'forbidden' });
        return;
      }
      const evt = req.body || {};
      // Minimal validation
      if (!evt || typeof evt !== 'object' || !evt.type || !evt.ts || !evt.visitorId) {
        res.status(400).json({ ok: false, error: 'invalid_event' });
        return;
      }
      // Attach server timestamps / ip hints without storing raw IP by default
      const receivedAt = new Date().toISOString();
      const ip = (req.headers['x-forwarded-for'] || req.socket?.remoteAddress || '').toString().split(',')[0].trim();
      const safeIpHash = hashIp(ip); // anonymize
      const toSave = { ...evt, receivedAt, ipHash: safeIpHash };
      await save(toSave, req);
      res.json({ ok: true });
    } catch (err) {
      res.status(500).json({ ok: false, error: String(err) });
    }
  }
}

// Add these functions after createReceiver
export function createAnalytics(storage) {
  return {
    // Count unique visitors
    async getUniqueVisitorCount() {
      const events = await storage.getAll();
      const uniqueVisitors = new Set(events.map(e => e.visitorId));
      return uniqueVisitors.size;
    },

    // Get all info for a specific visitor
    async getVisitorInfo(visitorId) {
      const events = await storage.getAll();
      const visitorEvents = events.filter(e => e.visitorId === visitorId);
      
      if (visitorEvents.length === 0) return null;
      
      const firstEvent = visitorEvents[0];
      const lastEvent = visitorEvents[visitorEvents.length - 1];
      
      return {
        visitorId,
        firstSeen: firstEvent.ts,
        lastSeen: lastEvent.ts,
        totalEvents: visitorEvents.length,
        client: firstEvent.client,
        events: visitorEvents
      };
    }
  };
}

// Very small non-crypto hash for anonymization (not for security-critical use)
function hashIp(input) {
  if (!input) return null;
  let h = 0; for (let i = 0; i < input.length; i++) { h = (h << 5) - h + input.charCodeAt(i); h |= 0; }
  return `ip_${Math.abs(h)}`;
}
