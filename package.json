{"name": "unique-visitor-tracker", "version": "1.0.0", "description": "Lightweight, privacy-first unique visitor and event tracking helper for web apps (browser + Node middleware)", "keywords": ["analytics", "visitor", "tracking", "unique", "events", "browser", "express", "middleware"], "license": "MIT", "type": "module", "main": "src/index.js", "module": "src/index.js", "exports": {".": {"import": "./src/index.js", "require": "./src/index.js", "types": "./types/index.d.ts"}, "./middleware/express": {"import": "./src/middleware/express.js", "require": "./src/middleware/express.js", "types": "./types/middleware/express.d.ts"}}, "files": ["src", "types", "README.md", "LICENSE"], "sideEffects": false, "engines": {"node": ">=16"}, "scripts": {"lint": "echo 'No linter configured'", "test": "node ./tests/smoke.js", "prepare": "node -e \"console.log('unique-visitor-tracker ready')\""}, "devDependencies": {}, "peerDependencies": {}}