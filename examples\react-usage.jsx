import React, { useEffect } from 'react';
import VisitorTracker from 'unique-visitor-tracker';

const tracker = new VisitorTracker({ endpoint: '/api/track', meta: { app: 'example' } });

export default function App() {
  useEffect(() => {
    tracker.trackPage(window.location.pathname);
  }, []);

  return (
    <button onClick={() => tracker.trackEvent('cta_click', { where: 'hero' })}>
      Click me
    </button>
  );
}