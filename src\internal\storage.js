/** Simple storage interfaces with graceful fallbacks. */

class MemoryStore {
  constructor() { this.map = new Map(); }
  get(k) { return this.map.get(k) || null; }
  set(k, v) { this.map.set(k, v); }
  remove(k) { this.map.delete(k); }
}

class LocalStore {
  constructor() { this.ok = typeof window !== 'undefined' && !!window.localStorage; }
  get(k) { try { return this.ok ? window.localStorage.getItem(k) : null; } catch { return null; } }
  set(k, v) { try { if (this.ok) window.localStorage.setItem(k, v); } catch {} }
  remove(k) { try { if (this.ok) window.localStorage.removeItem(k); } catch {} }
}

class CookieStore {
  constructor(days = 365) { this.days = days; this.ok = typeof document !== 'undefined'; }
  get(name) {
    if (!this.ok) return null;
    const m = document.cookie.match('(?:^|; )' + encodeURIComponent(name) + '=([^;]*)');
    return m ? decodeURIComponent(m[1]) : null;
  }
  set(name, value) {
    if (!this.ok) return;
    const d = new Date(); d.setTime(d.getTime() + this.days * 24 * 60 * 60 * 1000);
    document.cookie = `${encodeURIComponent(name)}=${encodeURIComponent(value)}; expires=${d.toUTCString()}; path=/; SameSite=Lax`;
  }
  remove(name) {
    if (!this.ok) return;
    document.cookie = `${encodeURIComponent(name)}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; SameSite=Lax`;
  }
}

export function getMemoryStore() { return new MemoryStore(); }
export function getLocalStore() { return new LocalStore(); }
export function getCookieStore() { return new CookieStore(); }

export function getStore(pref = 'auto') {
  if (pref === 'memory') return new MemoryStore();
  if (pref === 'localStorage') return new LocalStore();
  if (pref === 'cookie') return new CookieStore();
  // auto: prefer localStorage, fallback cookie, then memory
  const ls = new LocalStore();
  if (ls.ok) return ls;
  const cs = new CookieStore();
  if (cs.ok) return cs;
  return new MemoryStore();
}