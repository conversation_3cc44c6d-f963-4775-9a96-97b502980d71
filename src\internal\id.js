const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

function nanoid(size = 21) {
  let id = '';
  // Prefer crypto if available (browser & node >=15)
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    const bytes = new Uint8Array(size);
    crypto.getRandomValues(bytes);
    for (let i = 0; i < size; i++) id += alphabet[bytes[i] % alphabet.length];
    return id;
  }
  // Fallback: Math.random (not cryptographically strong, but acceptable for anon ids)
  for (let i = 0; i < size; i++) id += alphabet[Math.floor(Math.random() * alphabet.length)];
  return id;
}

export function generateId() {
  return `uvt_${nanoid(16)}`;
}